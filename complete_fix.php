<?php
// Complete Database Fix Script
echo "<h1>Complete Database Fix</h1>";

// Connect to database
$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Database connected successfully</p>";
    
    // Drop existing tables if they exist
    echo "<h2>Cleaning existing tables...</h2>";
    $tables = ['users', 'clients', 'products', 'sales', 'purchases', 'accounts', 'companies', 'parties', 'product_types', 'product_models', 'transactions', 'installments', 'stocks'];
    
    foreach ($tables as $table) {
        mysqli_query($con, "DROP TABLE IF EXISTS `$table`");
    }
    echo "<p style='color: green;'>✓ Tables cleaned</p>";
    
    // Create users table
    echo "<h2>Creating users table...</h2>";
    $create_users = "CREATE TABLE `users` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `users_email_unique` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_users)) {
        echo "<p style='color: green;'>✓ Users table created</p>";
        
        // Insert default users
        $users = [
            ['fsdfas', '<EMAIL>', '$2y$10$yyld9pHyzEJpX/8l/7hHdO6hcLNH7y0h8H4Yt32VfIiz55jGns6QC', '6kOtS258IUqZlRSTaXEL2WeTgXI8dl1enKlsqhH10Lahg3DP9WVmQGlU1yfJ'],
            ['Saad', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL] // password: saad508
        ];
        
        foreach ($users as $user) {
            $insert_user = "INSERT INTO `users` (`name`, `email`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES 
            ('{$user[0]}', '{$user[1]}', '{$user[2]}', " . ($user[3] ? "'{$user[3]}'" : "NULL") . ", NOW(), NOW())";
            
            if (mysqli_query($con, $insert_user)) {
                echo "<p style='color: green;'>✓ User created: {$user[1]}</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create user {$user[1]}: " . mysqli_error($con) . "</p>";
            }
        }
        
        // Create other essential tables
        echo "<h2>Creating other tables...</h2>";
        
        // Create companies table
        $create_companies = "CREATE TABLE `companies` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_companies);
        
        // Create parties table
        $create_parties = "CREATE TABLE `parties` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `address` text COLLATE utf8mb4_unicode_ci,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_parties);
        
        // Create product_types table
        $create_product_types = "CREATE TABLE `product_types` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_product_types);
        
        // Create product_models table
        $create_product_models = "CREATE TABLE `product_models` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `product_type_id` int(10) unsigned NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_product_models);
        
        // Create clients table
        $create_clients = "CREATE TABLE `clients` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `cnic` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `phone` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `father_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `cost` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `occupation` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `permanent_address` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
            `address` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
            `guarantor_id` int(10) unsigned DEFAULT NULL,
            `deleted_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            `account_number` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `type` int(11) NOT NULL DEFAULT '0',
            `photo` text COLLATE utf8mb4_unicode_ci,
            PRIMARY KEY (`id`),
            UNIQUE KEY `clients_phone_unique` (`phone`),
            UNIQUE KEY `clients_account_number_unique` (`account_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_clients);
        
        // Create products table
        $create_products = "CREATE TABLE `products` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
            `description` text COLLATE utf8mb4_unicode_ci,
            `price` decimal(8,2) NOT NULL,
            `company_id` int(10) unsigned NOT NULL,
            `product_type_id` int(10) unsigned NOT NULL,
            `product_model_id` int(10) unsigned NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_products);
        
        // Create purchases table
        $create_purchases = "CREATE TABLE `purchases` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `party_id` int(10) unsigned NOT NULL,
            `total_amount` decimal(10,2) NOT NULL,
            `purchase_date` date NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_purchases);
        
        // Create sales table
        $create_sales = "CREATE TABLE `sales` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `client_id` int(10) unsigned NOT NULL,
            `party_id` int(10) unsigned NOT NULL,
            `total_amount` decimal(10,2) NOT NULL,
            `sale_date` date NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_sales);
        
        // Create transactions table
        $create_transactions = "CREATE TABLE `transactions` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `client_id` int(10) unsigned NOT NULL,
            `party_id` int(10) unsigned NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `type` enum('sale','purchase') NOT NULL,
            `transaction_date` date NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_transactions);
        
        // Create installments table
        $create_installments = "CREATE TABLE `installments` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `transaction_id` int(10) unsigned NOT NULL,
            `amount` decimal(8,2) NOT NULL,
            `due_date` date NOT NULL,
            `paid_date` date DEFAULT NULL,
            `status` enum('pending','paid') DEFAULT 'pending',
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_installments);
        
        // Create stocks table
        $create_stocks = "CREATE TABLE `stocks` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `product_id` int(10) unsigned NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 0,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_stocks);
        
        // Create accounts table
        $create_accounts = "CREATE TABLE `accounts` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `client_id` int(10) unsigned NOT NULL,
            `product_id` int(10) unsigned NOT NULL,
            `purchase_id` int(10) unsigned NOT NULL,
            `total_installments` int(11) NOT NULL,
            `installment_amount` double NOT NULL,
            `installment_date` date NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        mysqli_query($con, $create_accounts);
        
        echo "<p style='color: green;'>✓ All tables created successfully</p>";
        
        // Insert sample data
        echo "<h2>Adding sample data...</h2>";
        
        // Add sample company
        mysqli_query($con, "INSERT INTO companies (name, created_at, updated_at) VALUES ('Sample Company', NOW(), NOW())");
        
        // Add sample party
        mysqli_query($con, "INSERT INTO parties (name, phone, address, created_at, updated_at) VALUES ('Sample Party', '***********', 'Sample Address', NOW(), NOW())");
        
        // Add sample product type
        mysqli_query($con, "INSERT INTO product_types (name, created_at, updated_at) VALUES ('Electronics', NOW(), NOW())");
        
        // Add sample product model
        mysqli_query($con, "INSERT INTO product_models (name, product_type_id, created_at, updated_at) VALUES ('Smartphone', 1, NOW(), NOW())");
        
        echo "<p style='color: green;'>✓ Sample data added</p>";
        
        // Success message
        echo "<h2 style='color: green;'>🎉 Database Setup Complete!</h2>";
        echo "<p>All tables have been created and your application is ready to use.</p>";
        
        echo "<h3>Available Users:</h3>";
        echo "<ul>";
        echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> fsdfas</li>";
        echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> saad508</li>";
        echo "</ul>";
        
        echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Launch Application</a></p>";
        
    } else {
        echo "<p style='color: red;'>✗ Failed to create users table: " . mysqli_error($con) . "</p>";
    }
    
    mysqli_close($con);
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
}
?>
