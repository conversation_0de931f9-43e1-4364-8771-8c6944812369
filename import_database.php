<?php
// Database Import Script
echo "<h1>Database Import Script</h1>";

// Connect to database
$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Connected to database successfully</p>";
    
    // Read the SQL file
    $sql_file = 'zbadmin.sql';
    if (file_exists($sql_file)) {
        echo "<p>✓ SQL file found: $sql_file</p>";
        
        $sql_content = file_get_contents($sql_file);
        
        // Split into individual queries
        $queries = explode(';', $sql_content);
        $success_count = 0;
        $error_count = 0;
        
        echo "<h2>Importing database tables...</h2>";
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query)) {
                if (mysqli_query($con, $query)) {
                    $success_count++;
                    echo "<p style='color: green;'>✓ Query executed successfully</p>";
                } else {
                    $error_count++;
                    echo "<p style='color: red;'>✗ Error: " . mysqli_error($con) . "</p>";
                }
            }
        }
        
        echo "<h3>Import Summary:</h3>";
        echo "<p>Successful queries: $success_count</p>";
        echo "<p>Failed queries: $error_count</p>";
        
        // Check if users table was created
        $result = mysqli_query($con, "SHOW TABLES LIKE 'users'");
        if (mysqli_num_rows($result) > 0) {
            echo "<p style='color: green;'>✓ Users table created successfully!</p>";
            
            // Check for users
            $user_count = mysqli_query($con, "SELECT COUNT(*) as count FROM users");
            $count = mysqli_fetch_array($user_count)['count'];
            echo "<p>Number of users: $count</p>";
            
            if ($count > 0) {
                echo "<p style='color: green;'>✓ Database import completed successfully!</p>";
                echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Users table was not created</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ SQL file not found: $sql_file</p>";
        echo "<p>Please make sure zbadmin.sql file exists in the same directory as this script.</p>";
    }
    
    mysqli_close($con);
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
}
?>
