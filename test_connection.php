<?php
// Simple database connection test
echo "<h1>Database Connection Test</h1>";

// Test connection with root password
$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Check if tables exist
    $result = mysqli_query($con, "SHOW TABLES");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✓ Database tables exist (" . mysqli_num_rows($result) . " tables)</p>";
        
        // Show table names
        echo "<h3>Available Tables:</h3><ul>";
        while ($row = mysqli_fetch_array($result)) {
            echo "<li>" . $row[0] . "</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: orange;'>⚠ Database is empty. Please import zbadmin.sql</p>";
    }
    
    mysqli_close($con);
    
    echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
    
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
    
    echo "<h2>Troubleshooting:</h2>";
    echo "<ol>";
    echo "<li>Make sure MySQL is running in XAMPP</li>";
    echo "<li>Check if database 'zbadmin' exists</li>";
    echo "<li>Verify root password is 'root'</li>";
    echo "<li>Try connecting with MySQL Workbench first</li>";
    echo "</ol>";
}
?>
