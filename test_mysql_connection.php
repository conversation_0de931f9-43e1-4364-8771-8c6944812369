<?php
// Test MySQL connection with different password combinations
echo "<h1>MySQL Connection Test</h1>";

$host = "localhost";
$user = "root";
$passwords = ["", "root", "admin", "password", "123456", "mysql"];

echo "<h2>Trying different password combinations...</h2>";

foreach ($passwords as $pass) {
    echo "<p>Trying password: " . ($pass ? $pass : "(empty)") . " ... ";
    
    $con = @mysqli_connect($host, $user, $pass);
    if ($con) {
        echo "<span style='color: green;'>✓ SUCCESS!</span></p>";
        echo "<p><strong>Working configuration:</strong></p>";
        echo "<ul>";
        echo "<li>Host: $host</li>";
        echo "<li>User: $user</li>";
        echo "<li>Password: " . ($pass ? $pass : "(empty)") . "</li>";
        echo "</ul>";
        
        // Test if we can create/access the database
        $result = mysqli_query($con, "SHOW DATABASES LIKE 'zbadmin'");
        if (mysqli_num_rows($result) > 0) {
            echo "<p style='color: green;'>✓ Database 'zbadmin' exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Database 'zbadmin' does not exist - will create it</p>";
            if (mysqli_query($con, "CREATE DATABASE `zbadmin` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")) {
                echo "<p style='color: green;'>✓ Database 'zbadmin' created successfully</p>";
            }
        }
        
        // Update db.php with working configuration
        $db_content = "<?php 
Class DB{
	public function connection(){	
		\$host = \"$host\";
		\$user = \"$user\";
		\$pass = \"" . addslashes($pass) . "\";
		\$db   = \"zbadmin\";	
		\$con = mysqli_connect(\$host,\$user,\$pass,\$db) or die(mysqli_error(\$con));
		return \$con;
	}
}
?>";
        
        if (file_put_contents('db.php', $db_content)) {
            echo "<p style='color: green;'>✓ Database configuration updated in db.php</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to update db.php file</p>";
        }
        
        echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
        
        mysqli_close($con);
        break;
    } else {
        echo "<span style='color: red;'>✗ Failed</span></p>";
    }
}

echo "<h2>If none of the above worked:</h2>";
echo "<ol>";
echo "<li>Open XAMPP Control Panel</li>";
echo "<li>Click on 'Shell' button next to MySQL</li>";
echo "<li>Type: <code>mysql -u root -p</code></li>";
echo "<li>Enter your MySQL root password</li>";
echo "<li>Once connected, type: <code>ALTER USER 'root'@'localhost' IDENTIFIED BY '';</code></li>";
echo "<li>Type: <code>FLUSH PRIVILEGES;</code></li>";
echo "<li>Type: <code>EXIT;</code></li>";
echo "<li>Restart MySQL service in XAMPP</li>";
echo "<li>Refresh this page</li>";
echo "</ol>";

echo "<h2>Alternative: Create a new MySQL user</h2>";
echo "<ol>";
echo "<li>Open phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
echo "<li>Go to 'User accounts' tab</li>";
echo "<li>Click 'Add user account'</li>";
echo "<li>Create a new user with username 'zbadmin' and password 'zbadmin123'</li>";
echo "<li>Grant all privileges on 'zbadmin' database</li>";
echo "<li>Update the db.php file with the new credentials</li>";
echo "</ol>";
?>
