<?php
// Database Setup Script for PHP Installment Sale/Purchase Inventory System
echo "<!DOCTYPE html>";
echo "<html><head><title>Database Setup</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; }";
echo ".success { color: green; }";
echo ".error { color: red; }";
echo ".warning { color: orange; }";
echo ".box { border: 1px solid #ccc; padding: 20px; margin: 10px 0; border-radius: 5px; }";
echo "</style></head><body>";

echo "<h1>Database Setup for PHP Inventory System</h1>";

// Check if form is submitted
if ($_POST) {
    $host = $_POST['host'] ?? 'localhost';
    $user = $_POST['user'] ?? 'root';
    $pass = $_POST['pass'] ?? '';
    $dbname = $_POST['dbname'] ?? 'zbadmin';
    
    echo "<div class='box'>";
    echo "<h2>Testing Database Connection...</h2>";
    
    // Test connection
    $con = mysqli_connect($host, $user, $pass);
    if ($con) {
        echo "<p class='success'>✓ Connected to MySQL server successfully</p>";
        
        // Create database if it doesn't exist
        $result = mysqli_query($con, "SHOW DATABASES LIKE '$dbname'");
        if (mysqli_num_rows($result) == 0) {
            if (mysqli_query($con, "CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")) {
                echo "<p class='success'>✓ Database '$dbname' created successfully</p>";
            } else {
                echo "<p class='error'>✗ Failed to create database: " . mysqli_error($con) . "</p>";
            }
        } else {
            echo "<p class='success'>✓ Database '$dbname' already exists</p>";
        }
        
        // Select database
        mysqli_select_db($con, $dbname);
        
        // Check if tables exist
        $result = mysqli_query($con, "SHOW TABLES");
        if (mysqli_num_rows($result) == 0) {
            echo "<p class='warning'>⚠ Database is empty. You need to import the zbadmin.sql file.</p>";
            echo "<p><a href='http://localhost/phpmyadmin' target='_blank'>Open phpMyAdmin to import SQL file</a></p>";
        } else {
            echo "<p class='success'>✓ Database tables exist</p>";
        }
        
        // Update db.php file
        $db_content = "<?php 
Class DB{
	public function connection(){	
		\$host = \"$host\";
		\$user = \"$user\";
		\$pass = \"$pass\";
		\$db   = \"$dbname\";	
		\$con = mysqli_connect(\$host,\$user,\$pass,\$db) or die(mysqli_error(\$con));
		return \$con;
	}
}
?>";
        
        if (file_put_contents('db.php', $db_content)) {
            echo "<p class='success'>✓ Database configuration updated in db.php</p>";
        } else {
            echo "<p class='error'>✗ Failed to update db.php file</p>";
        }
        
        echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
        
    } else {
        echo "<p class='error'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
        echo "<p>Please check your MySQL credentials and try again.</p>";
    }
    echo "</div>";
} else {
    // Show configuration form
    echo "<div class='box'>";
    echo "<h2>Database Configuration</h2>";
    echo "<form method='post'>";
    echo "<p><label>Host: <input type='text' name='host' value='localhost' required></label></p>";
    echo "<p><label>Username: <input type='text' name='user' value='root' required></label></p>";
    echo "<p><label>Password: <input type='password' name='pass' value=''></label></p>";
    echo "<p><label>Database Name: <input type='text' name='dbname' value='zbadmin' required></label></p>";
    echo "<p><input type='submit' value='Test Connection & Setup Database' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'></p>";
    echo "</form>";
    echo "</div>";
    
    echo "<div class='box'>";
    echo "<h2>Instructions</h2>";
    echo "<ol>";
    echo "<li>Make sure XAMPP MySQL service is running</li>";
    echo "<li>Enter your MySQL credentials (default: root with no password)</li>";
    echo "<li>Click 'Test Connection & Setup Database'</li>";
    echo "<li>If database is empty, import zbadmin.sql through phpMyAdmin</li>";
    echo "<li>Access the application at <a href='http://localhost/zbadmin/'>http://localhost/zbadmin/</a></li>";
    echo "</ol>";
    echo "</div>";
}

echo "</body></html>";
?>
