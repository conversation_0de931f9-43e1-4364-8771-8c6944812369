<?php
// Installation script for PHP Installment Sale/Purchase Inventory System
echo "<h1>PHP Installment Sale/Purchase Inventory System - Installation</h1>";

// Check PHP version
echo "<h2>System Requirements Check:</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
if (version_compare(PHP_VERSION, '5.6.0') >= 0) {
    echo "<p style='color: green;'>✓ PHP version is compatible</p>";
} else {
    echo "<p style='color: red;'>✗ PHP version is too old. Please upgrade to PHP 5.6 or higher.</p>";
}

// Check MySQL extension
if (extension_loaded('mysqli')) {
    echo "<p style='color: green;'>✓ MySQLi extension is available</p>";
} else {
    echo "<p style='color: red;'>✗ MySQLi extension is not available</p>";
}

// Check if we can connect to database
echo "<h2>Database Connection Test:</h2>";
try {
    $host = "localhost";
    $user = "root";
    $pass = "";
    $db   = "zbadmin";
    
    $con = mysqli_connect($host, $user, $pass);
    if ($con) {
        echo "<p style='color: green;'>✓ Database connection successful</p>";
        
        // Check if database exists
        $result = mysqli_query($con, "SHOW DATABASES LIKE 'zbadmin'");
        if (mysqli_num_rows($result) > 0) {
            echo "<p style='color: green;'>✓ Database 'zbadmin' exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Database 'zbadmin' does not exist. Creating...</p>";
            if (mysqli_query($con, "CREATE DATABASE zbadmin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")) {
                echo "<p style='color: green;'>✓ Database 'zbadmin' created successfully</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create database: " . mysqli_error($con) . "</p>";
            }
        }
        
        // Import SQL file if database is empty
        mysqli_select_db($con, $db);
        $result = mysqli_query($con, "SHOW TABLES");
        if (mysqli_num_rows($result) == 0) {
            echo "<p style='color: orange;'>⚠ Database is empty. Please import the zbadmin.sql file through phpMyAdmin</p>";
            echo "<p><a href='http://localhost/phpmyadmin' target='_blank'>Open phpMyAdmin</a></p>";
        } else {
            echo "<p style='color: green;'>✓ Database tables exist</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection error: " . $e->getMessage() . "</p>";
}

echo "<h2>Installation Instructions:</h2>";
echo "<ol>";
echo "<li>Make sure XAMPP is running (Apache and MySQL services)</li>";
echo "<li>Open phpMyAdmin at <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
echo "<li>Create a new database named 'zbadmin'</li>";
echo "<li>Import the zbadmin.sql file into the database</li>";
echo "<li>Access the application at <a href='http://localhost/zbadmin' target='_blank'>http://localhost/zbadmin</a></li>";
echo "</ol>";

echo "<h2>Default Login Credentials:</h2>";
echo "<p>You may need to check the database for default user credentials or create a new user.</p>";

echo "<h2>Next Steps:</h2>";
echo "<p><a href='http://localhost/zbadmin' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
?>
