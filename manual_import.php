<?php
// Manual Database Import
echo "<h1>Manual Database Import</h1>";

// Connect to database
$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Database connected</p>";
    
    // Create users table
    echo "<h2>Creating users table...</h2>";
    $create_users = "CREATE TABLE `users` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `users_email_unique` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_users)) {
        echo "<p style='color: green;'>✓ Users table created</p>";
        
        // Insert default user
        $insert_user = "INSERT INTO `users` (`name`, `email`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES 
        ('fsdfas', '<EMAIL>', '$2y$10$yyld9pHyzEJpX/8l/7hHdO6hcLNH7y0h8H4Yt32VfIiz55jGns6QC', '6kOtS258IUqZlRSTaXEL2WeTgXI8dl1enKlsqhH10Lahg3DP9WVmQGlU1yfJ', '2017-07-02 00:17:05', '2017-07-02 00:17:05')";
        
        if (mysqli_query($con, $insert_user)) {
            echo "<p style='color: green;'>✓ Default user created</p>";
            
            // Create other essential tables
            echo "<h2>Creating other tables...</h2>";
            
            // Create clients table
            $create_clients = "CREATE TABLE `clients` (
                `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                `cnic` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `phone` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                `father_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `cost` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `occupation` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `permanent_address` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
                `address` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
                `guarantor_id` int(10) unsigned DEFAULT NULL,
                `deleted_at` timestamp NULL DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                `account_number` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                `type` int(11) NOT NULL DEFAULT '0',
                `photo` text COLLATE utf8mb4_unicode_ci,
                PRIMARY KEY (`id`),
                UNIQUE KEY `clients_phone_unique` (`phone`),
                UNIQUE KEY `clients_account_number_unique` (`account_number`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if (mysqli_query($con, $create_clients)) {
                echo "<p style='color: green;'>✓ Clients table created</p>";
            }
            
            // Create products table
            $create_products = "CREATE TABLE `products` (
                `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                `description` text COLLATE utf8mb4_unicode_ci,
                `price` decimal(8,2) NOT NULL,
                `company_id` int(10) unsigned NOT NULL,
                `product_type_id` int(10) unsigned NOT NULL,
                `product_model_id` int(10) unsigned NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if (mysqli_query($con, $create_products)) {
                echo "<p style='color: green;'>✓ Products table created</p>";
            }
            
            echo "<h2 style='color: green;'>🎉 Basic tables created successfully!</h2>";
            echo "<p>You can now login to the application.</p>";
            echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Go to Application</a></p>";
            
            echo "<h3>Login Credentials:</h3>";
            echo "<ul>";
            echo "<li><strong>Email:</strong> <EMAIL></li>";
            echo "<li><strong>Password:</strong> fsdfas</li>";
            echo "</ul>";
            
        } else {
            echo "<p style='color: red;'>✗ Failed to create default user: " . mysqli_error($con) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Failed to create users table: " . mysqli_error($con) . "</p>";
    }
    
    mysqli_close($con);
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
}
?>
