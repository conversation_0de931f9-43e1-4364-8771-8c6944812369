<?php
// Check if database tables exist
echo "<h1>Database Tables Check</h1>";

$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Check if users table exists
    $result = mysqli_query($con, "SHOW TABLES LIKE 'users'");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✓ Users table exists</p>";
        
        // Check if there are users in the table
        $user_count = mysqli_query($con, "SELECT COUNT(*) as count FROM users");
        $count = mysqli_fetch_array($user_count)['count'];
        echo "<p>Number of users: $count</p>";
        
        if ($count > 0) {
            // Show available users
            $users = mysqli_query($con, "SELECT name, email FROM users LIMIT 5");
            echo "<h3>Available Users:</h3><ul>";
            while ($user = mysqli_fetch_array($users)) {
                echo "<li><strong>" . $user['name'] . "</strong> (" . $user['email'] . ")</li>";
            }
            echo "</ul>";
            
            echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login</a></p>";
        } else {
            echo "<p style='color: orange;'>⚠ Users table is empty</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Users table does not exist</p>";
        echo "<h2>You need to import the database!</h2>";
        echo "<ol>";
        echo "<li>Open MySQL Workbench</li>";
        echo "<li>Connect to your database (root/root)</li>";
        echo "<li>Select 'zbadmin' database</li>";
        echo "<li>File → Open SQL Script</li>";
        echo "<li>Select 'zbadmin.sql' file</li>";
        echo "<li>Click Execute</li>";
        echo "</ol>";
        
        echo "<p><strong>Or use phpMyAdmin:</strong></p>";
        echo "<ol>";
        echo "<li>Go to <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
        echo "<li>Login with root/root</li>";
        echo "<li>Select 'zbadmin' database</li>";
        echo "<li>Click 'Import' tab</li>";
        echo "<li>Choose 'zbadmin.sql' file</li>";
        echo "<li>Click 'Go'</li>";
        echo "</ol>";
    }
    
    mysqli_close($con);
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
}
?>
