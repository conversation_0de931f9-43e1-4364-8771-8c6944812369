@echo off
echo MySQL Root Password Reset Tool
echo =============================
echo.

echo This script will help you reset the MySQL root password.
echo.
echo Step 1: Stop MySQL service...
echo Please stop MySQL service in XAMPP Control Panel first.
echo.
pause

echo.
echo Step 2: Starting MySQL in safe mode...
echo Please add 'skip-grant-tables' to your my.ini file and start MySQL.
echo.
echo Instructions:
echo 1. In XAMPP Control Panel, click "Config" next to MySQL
echo 2. Select "my.ini"
echo 3. Find [mysqld] section and add: skip-grant-tables
echo 4. Save the file and start MySQL
echo.
pause

echo.
echo Step 3: Resetting password...
cd /d "C:\xampp\mysql\bin"
echo Connecting to MySQL...
mysql -u root -e "USE mysql; UPDATE user SET authentication_string='' WHERE user='root'; FLUSH PRIVILEGES;"

echo.
echo Step 4: Password reset complete!
echo.
echo Now:
echo 1. Stop MySQL service
echo 2. Remove 'skip-grant-tables' from my.ini
echo 3. Start MySQL service
echo 4. Test the application at: http://localhost/zbadmin/
echo.
pause
