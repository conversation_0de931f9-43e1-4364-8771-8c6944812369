# Fix MySQL Password Issue - Step by Step Guide

## Problem
You're getting this error:
```
Access denied for user 'root'@'localhost' (using password: NO)
```

This means your MySQL root user has a password, but the application is trying to connect without one.

## Solution 1: Reset MySQL Root Password (Recommended)

### Step 1: Stop MySQL Service
1. Open XAMPP Control Panel
2. Click "Stop" next to MySQL
3. Wait for it to stop completely

### Step 2: Start MySQL in Safe Mode
1. In XAMPP Control Panel, click "Config" next to MySQL
2. Select "my.ini"
3. Find the line `[mysqld]` and add this line below it:
   ```
   skip-grant-tables
   ```
4. Save the file
5. Start MySQL service again

### Step 3: Reset Password
1. Open Command Prompt as Administrator
2. Navigate to MySQL bin directory:
   ```
   cd C:\xampp\mysql\bin
   ```
3. Connect to MySQL:
   ```
   mysql -u root
   ```
4. Reset the password:
   ```sql
   USE mysql;
   UPDATE user SET authentication_string='' WHERE user='root';
   FLUSH PRIVILEGES;
   EXIT;
   ```

### Step 4: Restore Normal Mode
1. Stop MySQL service
2. Remove the `skip-grant-tables` line from my.ini
3. Save the file
4. Start MySQL service

### Step 5: Test Connection
1. Open your browser
2. Go to: `http://localhost/zbadmin/`
3. The application should now work!

## Solution 2: Use phpMyAdmin to Create New User

### Step 1: Access phpMyAdmin
1. Open your browser
2. Go to: `http://localhost/phpmyadmin`
3. Login with your current root password

### Step 2: Create New User
1. Click on "User accounts" tab
2. Click "Add user account"
3. Fill in:
   - User name: `zbadmin`
   - Host name: `localhost`
   - Password: `zbadmin123`
4. Check "Create database with same name and grant all privileges"
5. Click "Go"

### Step 3: Update Application Configuration
1. Edit the file `db.php`
2. Change the credentials to:
   ```php
   $host = "localhost";
   $user = "zbadmin";
   $pass = "zbadmin123";
   $db   = "zbadmin";
   ```

## Solution 3: Quick Test Script

If you want to test different passwords automatically:

1. Copy the `test_mysql_connection.php` file to `C:\xampp\htdocs\zbadmin\`
2. Open: `http://localhost/zbadmin/test_mysql_connection.php`
3. The script will try common passwords and update the configuration automatically

## Verification

After fixing the password issue:

1. Go to: `http://localhost/zbadmin/`
2. If you still get errors, check:
   - MySQL service is running
   - Database 'zbadmin' exists
   - Import the `zbadmin.sql` file through phpMyAdmin

## Import Database

1. Go to: `http://localhost/phpmyadmin`
2. Select the `zbadmin` database
3. Click "Import" tab
4. Choose the `zbadmin.sql` file
5. Click "Go"

## Default Login Credentials

After importing the database, check the `users` table in phpMyAdmin for default login credentials, or create a new user manually.

---

**Note**: Solution 1 (resetting root password) is the simplest and most reliable approach for development environments.
