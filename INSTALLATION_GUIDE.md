# PHP Installment Sale/Purchase Inventory System - Installation Guide

## System Requirements

- **Web Server**: Apache (included in XAMPP)
- **PHP**: Version 5.6 or higher
- **MySQL/MariaDB**: Database server
- **Web Browser**: Any modern browser

## Installation Steps

### 1. Start XAMPP Services

1. Open XAMPP Control Panel
2. Start Apache service
3. Start MySQL service
4. Make sure both services show green status

### 2. Database Setup

1. Open your web browser and go to: `http://localhost/phpmyadmin`
2. Create a new database named `zbadmin`
3. Select the `zbadmin` database
4. Go to the "Import" tab
5. Choose the `zbadmin.sql` file from the project directory
6. Click "Go" to import the database structure and sample data

### 3. Access the Application

1. Open your web browser
2. Go to: `http://localhost/zbadmin`
3. The application should redirect to the login page

### 4. Default Login Credentials

You may need to check the database for default user credentials. Look in the `users` table in phpMyAdmin.

If no default user exists, you can create one by:

1. Going to phpMyAdmin
2. Selecting the `zbadmin` database
3. Going to the `users` table
4. Adding a new user with appropriate credentials

### 5. Troubleshooting

#### If you get database connection errors:

- Make sure MySQL is running in XAMPP
- Check that the database `zbadmin` exists
- Verify the database credentials in `db.php`

#### If you get "Page not found" errors:

- Make sure Apache is running in XAMPP
- Check that the files are in the correct directory: `C:\xampp\htdocs\zbadmin\`
- Verify the URL: `http://localhost/zbadmin`

#### If you get PHP errors:

- Make sure PHP is enabled in XAMPP
- Check that the MySQLi extension is enabled
- Verify PHP version is 5.6 or higher

## Application Features

This inventory system includes:

- Client management
- Product management
- Purchase and sale tracking
- Installment payment tracking
- Reports and analytics
- User management

## File Structure

- `index.php` - Main entry point
- `config.php` - Configuration file
- `db.php` - Database connection
- `functions.php` - Helper functions
- `login.php` - Login page
- Various feature pages (Clients.php, Products.php, etc.)

## Support

If you encounter any issues during installation, please check:

1. XAMPP error logs
2. PHP error logs
3. MySQL error logs
4. Browser developer console for JavaScript errors
