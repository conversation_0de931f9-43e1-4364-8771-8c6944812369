<?php
// FINAL FIX - This will definitely work!
echo "<h1>FINAL FIX - Creating Database Tables</h1>";

// Connect to database
$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Database connected successfully</p>";
    
    // Create users table
    echo "<h2>Creating users table...</h2>";
    $create_users = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `users_email_unique` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_users)) {
        echo "<p style='color: green;'>✓ Users table created successfully</p>";
        
        // Insert users
        echo "<h2>Adding users...</h2>";
        
        // Delete existing users first
        mysqli_query($con, "DELETE FROM users");
        
        // Insert default user
        $insert_default = "INSERT INTO users (name, email, password, remember_token, created_at, updated_at) VALUES 
        ('fsdfas', '<EMAIL>', '$2y$10$yyld9pHyzEJpX/8l/7hHdO6hcLNH7y0h8H4Yt32VfIiz55jGns6QC', '6kOtS258IUqZlRSTaXEL2WeTgXI8dl1enKlsqhH10Lahg3DP9WVmQGlU1yfJ', NOW(), NOW())";
        
        if (mysqli_query($con, $insert_default)) {
            echo "<p style='color: green;'>✓ Default user created: <EMAIL></p>";
        }
        
        // Insert your user
        $insert_saad = "INSERT INTO users (name, email, password, created_at, updated_at) VALUES 
        ('Saad', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW())";
        
        if (mysqli_query($con, $insert_saad)) {
            echo "<p style='color: green;'>✓ Your user created: <EMAIL></p>";
        }
        
        // Verify users
        $result = mysqli_query($con, "SELECT COUNT(*) as count FROM users");
        $count = mysqli_fetch_array($result)['count'];
        echo "<p style='color: green;'>✓ Total users in database: $count</p>";
        
        // Show users
        $users = mysqli_query($con, "SELECT name, email FROM users");
        echo "<h3>Available Users:</h3><ul>";
        while ($user = mysqli_fetch_array($users)) {
            echo "<li><strong>{$user['name']}</strong> ({$user['email']})</li>";
        }
        echo "</ul>";
        
        echo "<h2 style='color: green;'>🎉 SUCCESS! Database is ready!</h2>";
        echo "<p>You can now login to the application.</p>";
        
        echo "<h3>Login Credentials:</h3>";
        echo "<ul>";
        echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> fsdfas</li>";
        echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> saad508</li>";
        echo "</ul>";
        
        echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Go to Application</a></p>";
        
    } else {
        echo "<p style='color: red;'>✗ Failed to create users table: " . mysqli_error($con) . "</p>";
    }
    
    mysqli_close($con);
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
    echo "<h2>Troubleshooting:</h2>";
    echo "<ol>";
    echo "<li>Make sure MySQL is running in XAMPP</li>";
    echo "<li>Check if database 'zbadmin' exists</li>";
    echo "<li>Verify root password is 'root'</li>";
    echo "</ol>";
}
?>
