<?php
// Final test script to verify complete setup
echo "<h1>PHP Inventory System - Final Test</h1>";

// Test 1: Database Connection
echo "<h2>Test 1: Database Connection</h2>";
$con = mysqli_connect("localhost", "root", "root", "zbadmin");
if ($con) {
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Test 2: Check Tables
    echo "<h2>Test 2: Database Tables</h2>";
    $result = mysqli_query($con, "SHOW TABLES");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✓ Database tables exist (" . mysqli_num_rows($result) . " tables)</p>";
        
        // Show important tables
        echo "<h3>Key Tables Found:</h3><ul>";
        $tables = [];
        while ($row = mysqli_fetch_array($result)) {
            $tables[] = $row[0];
        }
        
        $important_tables = ['users', 'clients', 'products', 'sales', 'purchases'];
        foreach ($important_tables as $table) {
            if (in_array($table, $tables)) {
                echo "<li style='color: green;'>✓ $table</li>";
            } else {
                echo "<li style='color: red;'>✗ $table (missing)</li>";
            }
        }
        echo "</ul>";
        
        // Test 3: Check Users
        echo "<h2>Test 3: User Accounts</h2>";
        $user_result = mysqli_query($con, "SELECT name, email FROM users LIMIT 5");
        if (mysqli_num_rows($user_result) > 0) {
            echo "<p style='color: green;'>✓ User accounts found</p>";
            echo "<h3>Available Users:</h3><ul>";
            while ($user = mysqli_fetch_array($user_result)) {
                echo "<li><strong>" . $user['name'] . "</strong> (" . $user['email'] . ")</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠ No user accounts found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ No tables found in database</p>";
    }
    
    mysqli_close($con);
    
    // Test 4: Application Files
    echo "<h2>Test 4: Application Files</h2>";
    $required_files = ['index.php', 'config.php', 'db.php', 'functions.php', 'login.php'];
    $missing_files = [];
    
    foreach ($required_files as $file) {
        if (file_exists("C:/xampp/htdocs/zbadmin/$file")) {
            echo "<p style='color: green;'>✓ $file</p>";
        } else {
            echo "<p style='color: red;'>✗ $file (missing)</p>";
            $missing_files[] = $file;
        }
    }
    
    if (empty($missing_files)) {
        echo "<p style='color: green;'>✓ All required files present</p>";
    }
    
    // Success message
    echo "<h2 style='color: green;'>🎉 Setup Complete!</h2>";
    echo "<p>Your PHP Inventory System is ready to use.</p>";
    echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Launch Application</a></p>";
    
    echo "<h3>Default Login Credentials:</h3>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Password:</strong> fsdfas</li>";
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
    echo "<h2>Troubleshooting:</h2>";
    echo "<ol>";
    echo "<li>Make sure MySQL is running in XAMPP</li>";
    echo "<li>Check if database 'zbadmin' exists</li>";
    echo "<li>Verify root password is 'root'</li>";
    echo "<li>Import zbadmin.sql file if database is empty</li>";
    echo "</ol>";
}
?>
