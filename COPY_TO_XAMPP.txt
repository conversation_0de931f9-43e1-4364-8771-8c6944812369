=== INSTRUCTIONS TO FIX LOGIN ISSUE ===

1. Copy the content below and save it as "FIX_LOGIN.php" in C:\xampp\htdocs\
2. Open your browser and go to: http://localhost/FIX_LOGIN.php
3. This will create the users table and fix the login issue
4. Then go to: http://localhost/zbadmin/login.php

=== SCRIPT CONTENT TO COPY ===

<?php
// FIX_LOGIN.php - Fix the doLogin.php issue
echo "<h1>Fixing Login Issue</h1>";
echo "<p>This script will create the missing database tables and fix the login problem.</p>";

// Connect to database
$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Database connected successfully</p>";
    
    // Step 1: Create users table
    echo "<h2>Step 1: Creating users table...</h2>";
    $create_users = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `users_email_unique` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_users)) {
        echo "<p style='color: green;'>✓ Users table created successfully</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create users table: " . mysqli_error($con) . "</p>";
    }
    
    // Step 2: Insert users
    echo "<h2>Step 2: Adding user accounts...</h2>";
    
    // Clear existing users
    mysqli_query($con, "DELETE FROM users");
    
    // Insert default user (password: fsdfas)
    $insert_default = "INSERT INTO users (name, email, password, remember_token, created_at, updated_at) VALUES 
    ('fsdfas', '<EMAIL>', '" . md5('fsdfas') . "', '6kOtS258IUqZlRSTaXEL2WeTgXI8dl1enKlsqhH10Lahg3DP9WVmQGlU1yfJ', NOW(), NOW())";
    
    if (mysqli_query($con, $insert_default)) {
        echo "<p style='color: green;'>✓ Default user created: <EMAIL> (password: fsdfas)</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create default user: " . mysqli_error($con) . "</p>";
    }
    
    // Insert your user (password: saad508)
    $insert_saad = "INSERT INTO users (name, email, password, created_at, updated_at) VALUES 
    ('<EMAIL>', '<EMAIL>', '" . md5('saad508') . "', NOW(), NOW())";
    
    if (mysqli_query($con, $insert_saad)) {
        echo "<p style='color: green;'>✓ Your user created: <EMAIL> (password: saad508)</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create your user: " . mysqli_error($con) . "</p>";
    }
    
    // Step 3: Verify users
    echo "<h2>Step 3: Verifying users...</h2>";
    $result = mysqli_query($con, "SELECT COUNT(*) as count FROM users");
    $count = mysqli_fetch_array($result)['count'];
    echo "<p style='color: green;'>✓ Total users in database: $count</p>";
    
    // Show users
    $users = mysqli_query($con, "SELECT name, email FROM users");
    echo "<h3>Available Users:</h3><ul>";
    while ($user = mysqli_fetch_array($users)) {
        echo "<li><strong>{$user['name']}</strong> ({$user['email']})</li>";
    }
    echo "</ul>";
    
    // Step 4: Test login query
    echo "<h2>Step 4: Testing login query...</h2>";
    $test_query = "SELECT id FROM users WHERE (name='<EMAIL>' OR email='<EMAIL>') AND password='" . md5('saad508') . "'";
    $test_result = mysqli_query($con, $test_query);
    
    if ($test_result) {
        $user_count = mysqli_num_rows($test_result);
        if ($user_count == 1) {
            echo "<p style='color: green;'>✓ Login query works correctly!</p>";
        } else {
            echo "<p style='color: red;'>✗ Login query returned $user_count results (should be 1)</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Login query failed: " . mysqli_error($con) . "</p>";
    }
    
    mysqli_close($con);
    
    echo "<h2 style='color: green;'>🎉 Login Issue Fixed!</h2>";
    echo "<p>The users table has been created and your login should now work.</p>";
    
    echo "<h3>Login Credentials:</h3>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> fsdfas</li>";
    echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> saad508</li>";
    echo "</ul>";
    
    echo "<p><a href='http://localhost/zbadmin/login.php' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Go to Login Page</a></p>";
    
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
    echo "<h2>Troubleshooting:</h2>";
    echo "<ol>";
    echo "<li>Make sure MySQL is running in XAMPP</li>";
    echo "<li>Check if database 'zbadmin' exists</li>";
    echo "<li>Verify root password is 'root'</li>";
    echo "</ol>";
}
?>

=== END OF SCRIPT ===
