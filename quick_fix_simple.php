<?php
// Quick Simple Fix - Just create users table
echo "<h1>Quick Fix - Creating Users Table</h1>";

// Connect to database
$con = mysqli_connect("localhost", "root", "root", "zbadmin");

if ($con) {
    echo "<p style='color: green;'>✓ Database connected</p>";
    
    // Create users table
    $create_users = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
        `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `users_email_unique` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if (mysqli_query($con, $create_users)) {
        echo "<p style='color: green;'>✓ Users table created</p>";
        
        // Check if users exist
        $check_users = mysqli_query($con, "SELECT COUNT(*) as count FROM users");
        $user_count = mysqli_fetch_array($check_users)['count'];
        
        if ($user_count == 0) {
            // Insert users
            $users = [
                ['fsdfas', '<EMAIL>', '$2y$10$yyld9pHyzEJpX/8l/7hHdO6hcLNH7y0h8H4Yt32VfIiz55jGns6QC', '6kOtS258IUqZlRSTaXEL2WeTgXI8dl1enKlsqhH10Lahg3DP9WVmQGlU1yfJ'],
                ['Saad', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL]
            ];
            
            foreach ($users as $user) {
                $insert_user = "INSERT INTO `users` (`name`, `email`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES 
                ('{$user[0]}', '{$user[1]}', '{$user[2]}', " . ($user[3] ? "'{$user[3]}'" : "NULL") . ", NOW(), NOW())";
                
                if (mysqli_query($con, $insert_user)) {
                    echo "<p style='color: green;'>✓ User created: {$user[1]}</p>";
                } else {
                    echo "<p style='color: red;'>✗ Failed to create user {$user[1]}: " . mysqli_error($con) . "</p>";
                }
            }
        } else {
            echo "<p style='color: green;'>✓ Users already exist ($user_count users)</p>";
        }
        
        echo "<h2 style='color: green;'>🎉 Users table ready!</h2>";
        echo "<p>You can now login to the application.</p>";
        
        echo "<h3>Login Credentials:</h3>";
        echo "<ul>";
        echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> fsdfas</li>";
        echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> saad508</li>";
        echo "</ul>";
        
        echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Go to Application</a></p>";
        
    } else {
        echo "<p style='color: red;'>✗ Failed to create users table: " . mysqli_error($con) . "</p>";
    }
    
    mysqli_close($con);
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
}
?>
