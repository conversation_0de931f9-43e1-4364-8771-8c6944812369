<?php
// Quick fix for MySQL connection issue
echo "<h1>MySQL Connection Quick Fix</h1>";

// Test different password combinations
$passwords = ["", "root", "admin", "password", "123456", "mysql"];

foreach ($passwords as $pass) {
    echo "<p>Trying password: " . ($pass ? $pass : "(empty)") . " ... ";
    
    $con = @mysqli_connect("localhost", "root", $pass);
    if ($con) {
        echo "<span style='color: green;'>✓ SUCCESS!</span></p>";
        
        // Create database if it doesn't exist
        mysqli_query($con, "CREATE DATABASE IF NOT EXISTS `zbadmin` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Update the db.php file
        $db_content = '<?php 
Class DB{
	public function connection(){	
		$host = "localhost";
		$user = "root";
		$pass = "' . addslashes($pass) . '";
		$db   = "zbadmin";	
		$con = mysqli_connect($host,$user,$pass,$db) or die(mysqli_error($con));
		return $con;
	}
}
?>';
        
        // Update both the current directory and XAMPP directory
        file_put_contents('db.php', $db_content);
        file_put_contents('C:/xampp/htdocs/zbadmin/db.php', $db_content);
        
        echo "<p style='color: green;'>✓ Database configuration updated!</p>";
        echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
        
        mysqli_close($con);
        break;
    } else {
        echo "<span style='color: red;'>✗ Failed</span></p>";
    }
}

echo "<h2>If none worked, try this:</h2>";
echo "<ol>";
echo "<li>Open XAMPP Control Panel</li>";
echo "<li>Stop MySQL service</li>";
echo "<li>Click 'Config' next to MySQL</li>";
echo "<li>Select 'my.ini'</li>";
echo "<li>Find [mysqld] section and add: <code>skip-grant-tables</code></li>";
echo "<li>Save and start MySQL</li>";
echo "<li>Open Command Prompt and run: <code>C:\\xampp\\mysql\\bin\\mysql -u root</code></li>";
echo "<li>Type: <code>USE mysql; UPDATE user SET authentication_string='' WHERE user='root'; FLUSH PRIVILEGES; EXIT;</code></li>";
echo "<li>Stop MySQL, remove skip-grant-tables, start MySQL</li>";
echo "<li>Refresh this page</li>";
echo "</ol>";
?>
