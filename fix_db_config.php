<?php
// Fix database configuration in XAMPP directory
echo "<h1>Fixing Database Configuration</h1>";

// New database configuration with correct password
$db_content = '<?php 
Class DB{
	public function connection(){	
		$host = "localhost";
		$user = "root";
		$pass = "root";  // Your MySQL root password
		$db   = "zbadmin";	
		$con = mysqli_connect($host,$user,$pass,$db) or die(mysqli_error($con));
		return $con;
	}
}
?>';

// Update the db.php file in XAMPP directory
$xampp_path = 'C:/xampp/htdocs/zbadmin/db.php';

if (file_put_contents($xampp_path, $db_content)) {
    echo "<p style='color: green;'>✓ Database configuration updated successfully!</p>";
    echo "<p>File updated: $xampp_path</p>";
    
    // Test the connection
    echo "<h2>Testing Database Connection...</h2>";
    
    $con = mysqli_connect("localhost", "root", "root", "zbadmin");
    if ($con) {
        echo "<p style='color: green;'>✓ Database connection successful!</p>";
        
        // Check if tables exist
        $result = mysqli_query($con, "SHOW TABLES");
        if (mysqli_num_rows($result) > 0) {
            echo "<p style='color: green;'>✓ Database tables exist</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Database is empty. Please import zbadmin.sql</p>";
        }
        
        mysqli_close($con);
    } else {
        echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
    }
    
    echo "<p><a href='http://localhost/zbadmin/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
    
} else {
    echo "<p style='color: red;'>✗ Failed to update database configuration</p>";
    echo "<p>Please manually update the file: $xampp_path</p>";
    
    echo "<h2>Manual Update Instructions:</h2>";
    echo "<ol>";
    echo "<li>Open file: $xampp_path</li>";
    echo "<li>Replace the content with:</li>";
    echo "<pre>" . htmlspecialchars($db_content) . "</pre>";
    echo "<li>Save the file</li>";
    echo "<li>Refresh this page</li>";
    echo "</ol>";
}
?>
